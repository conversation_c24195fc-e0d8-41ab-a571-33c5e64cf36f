from flask import Flask, request, jsonify, render_template
import base64
import os
from werkzeug.utils import secure_filename
import json

# Temporary mock functions for testing without heavy dependencies
def agent_generator(images_base64):
    """Mock function to test multiple image functionality"""
    if isinstance(images_base64, list):
        num_images = len(images_base64)
        if num_images > 1:
            # Simulate image selection for multiple images
            selected_index = 0  # Always select first image for testing
            return {
                "title": f"Test Product (from {num_images} images)",
                "description": f"This is a test description generated from {num_images} uploaded images. Selected image {selected_index + 1} for analysis.",
                "selected_image_index": selected_index,
                "selection_reasoning": f"Selected image {selected_index + 1} out of {num_images} images as it appears to show the full object clearly.",
                "total_images": num_images
            }
        else:
            # Single image
            return {
                "title": "Test Product (single image)",
                "description": "This is a test description generated from a single uploaded image.",
                "total_images": 1
            }
    else:
        # Legacy format
        return {
            "title": "Test Product (legacy)",
            "description": "This is a test description from legacy single image format."
        }

def ingest_catalog_pdf(pdf_base64):
    print('ingest_catalog_pdf - mock function')
    return

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024
app.config['UPLOAD_FOLDER'] = 'uploads'

os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'pdf'}
ALLOWED_IMAGE_EXTENSIONS = {'jpg', 'jpeg', 'png', 'webp'}


def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def allowed_image_file(filename):
    """Check if image file extension is allowed"""
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in ALLOWED_IMAGE_EXTENSIONS




def upload_method(pdf_file):
    try:
        pdf_data = pdf_file.read()

        # Convert to base64
        pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')

        # Feed to the static function
        ingest_catalog_pdf(pdf_base64)

        filename = secure_filename(pdf_file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        pdf_file.save(file_path)

        return {
            "status": "success",
            "message": "PDF uploaded and processed successfully",
            "filename": filename,
            "file_size": os.path.getsize(file_path)
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Error processing PDF: {str(e)}"
        }


@app.route('/')
def index():
    return render_template('upload_pdf.html')


@app.route('/upload_image')
def upload_image_page():
    return render_template('upload_image.html')



@app.route('/upload_catalog', methods=['POST'])
def upload_catalog():
    try:
        if 'file' not in request.files:
            return jsonify({
                "status": "error",
                "message": "No file provided"
            }), 400

        file = request.files['file']

        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "No file selected"
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                "status": "error",
                "message": "Only PDF files are allowed"
            }), 400

        # Process the file using upload_method
        result = upload_method(file)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error processing upload: {str(e)}"
        }), 500


@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()

        # Support both single image (backward compatibility) and multiple images
        images_base64 = None
        if 'images_base64' in data:
            # New multiple images format
            images_base64 = data['images_base64']
            if not isinstance(images_base64, list) or len(images_base64) == 0:
                return jsonify({
                    "status": "error",
                    "message": "images_base64 must be a non-empty array"
                }), 400
        elif 'image_base64' in data:
            # Legacy single image format
            images_base64 = [data['image_base64']]
        else:
            return jsonify({
                "status": "error",
                "message": "Missing image_base64 or images_base64 in request"
            }), 400

        result = agent_generator(images_base64)

        return jsonify({
            "status": "success",
            "data": result
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error processing request: {str(e)}"
        }), 500


@app.errorhandler(413)
def too_large(e):
    """Handle file too large error"""
    return jsonify({
        "status": "error",
        "message": "File too large. Maximum size is 10MB."
    }), 413


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)