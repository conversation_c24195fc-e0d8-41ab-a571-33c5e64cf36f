from flask import Flask, request, jsonify, render_template
import base64
import os
from werkzeug.utils import secure_filename
import json
from generator_utils import *
from pdf_ingestion import *

# def agent_generator(image_base64):
#     return {
#         "title": "test",
#         "description": "test"
#     }

# def ingest_catalog_pdf(pdf_base64):
#     print('ingest_catalog_pdf')
#     return

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024
app.config['UPLOAD_FOLDER'] = 'uploads'

os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'pdf'}
ALLOWED_IMAGE_EXTENSIONS = {'jpg', 'jpeg', 'png', 'webp'}


def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def allowed_image_file(filename):
    """Check if image file extension is allowed"""
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in ALLOWED_IMAGE_EXTENSIONS




def upload_method(pdf_file):
    try:
        pdf_data = pdf_file.read()

        # Convert to base64
        pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')

        # Feed to the static function
        ingest_catalog_pdf(pdf_base64)

        filename = secure_filename(pdf_file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        pdf_file.save(file_path)

        return {
            "status": "success",
            "message": "PDF uploaded and processed successfully",
            "filename": filename,
            "file_size": os.path.getsize(file_path)
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Error processing PDF: {str(e)}"
        }


@app.route('/')
def index():
    return render_template('upload_pdf.html')


@app.route('/upload_image')
def upload_image_page():
    return render_template('upload_image.html')



@app.route('/upload_catalog', methods=['POST'])
def upload_catalog():
    try:
        if 'file' not in request.files:
            return jsonify({
                "status": "error",
                "message": "No file provided"
            }), 400

        file = request.files['file']

        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "No file selected"
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                "status": "error",
                "message": "Only PDF files are allowed"
            }), 400

        # Process the file using upload_method
        result = upload_method(file)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error processing upload: {str(e)}"
        }), 500


@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()

        if not data or 'image_base64' not in data:
            return jsonify({
                "status": "error",
                "message": "Missing image_base64 in request"
            }), 400

        image_base64 = data['image_base64']

        result = agent_generator(image_base64)

        return jsonify({
            "status": "success",
            "data": result
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error processing request: {str(e)}"
        }), 500


@app.errorhandler(413)
def too_large(e):
    """Handle file too large error"""
    return jsonify({
        "status": "error",
        "message": "File too large. Maximum size is 10MB."
    }), 413


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)