import torch
from PIL import Image
import numpy as np
import os
import io 
import base64

device = "cuda" if torch.cuda.is_available() else "cpu"



def base64_to_pil_image(image_base64):
    """
    Convert base64 string to PIL Image
    
    Args:
        image_base64 (str): Base64 encoded image string
    
    Returns:
        PIL.Image: PIL Image object
    """
    # Decode base64 string
    image_data = base64.b64decode(image_base64)
    
    # Convert to PIL Image
    image = Image.open(io.BytesIO(image_data))
    
    return image

from transformers import CLIPProcessor, CLIPModel

# Load model and processor
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32").to(device)
processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

def get_image_embedding(pil_image):
    """
    Get image embedding using Hugging Face transformers
    
    Args:
        pil_image (PIL.Image): PIL Image object
    
    Returns:
        numpy.ndarray: Image embedding vector
    """

    
    # Process image
    inputs = processor(images=pil_image, return_tensors="pt").to(device)  # Move inputs to device

    # Get image features
    with torch.no_grad():
        image_features = model.get_image_features(**inputs)
        # Normalize
        image_features = image_features / image_features.norm(dim=-1, keepdim=True)

    return image_features.cpu().numpy().flatten()  # Move back to CPU for numpy

import pandas as pd
import chromadb
from chromadb.config import Settings

# Setup ChromaDB
VECTOR_DIR = "./chroma_db"
settings = Settings(
    is_persistent=True,
    persist_directory=VECTOR_DIR,
    anonymized_telemetry=False,
)

client = chromadb.PersistentClient(settings=settings)


def search_similar_images_from_base64(image_base64, collection, k=5):
    """
    Search for similar images using base64 encoded image
    
    Args:
        image_base64 (str): Base64 encoded image string
        k (int): Number of similar images to return
    
    Returns:
        list: List of dictionaries with similar items
    """
    # Convert base64 to PIL image
    pil_image = base64_to_pil_image(image_base64)
    
    # Get query embedding
    query_embedding = get_image_embedding(pil_image)
    
    # Search
    results = collection.query(
        query_embeddings=[query_embedding.tolist()],
        n_results=k
    )
    
    # Convert to list of dicts with CSV structure
    similar_items = []
    for i, metadata in enumerate(results['metadatas'][0]):
        similar_items.append({
            'item_number': metadata['item_number'],
            'title': metadata['title'],
            'description': metadata['description'],
            'page': metadata['page'],
            'image_path': metadata['image_path'],
            'similarity_score': results['distances'][0][i]
        })
    
    return similar_items


from langchain_openai import ChatOpenAI
import os
import base64
from PIL import Image
from langchain.schema import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import Optional, List

from dotenv import load_dotenv
load_dotenv()
api_key = os.getenv('OPENAI_API_KEY')


# Pydantic model for structured output
class ItemDescription(BaseModel):
    title: str = Field(description="Concise, descriptive title mentioning the main type of furniture and key distinguishing features")
    description: str = Field(description="Detailed description following auction catalog style, starting with 'This [item type] features...'")

# Pydantic model for image selection
class ImageSelection(BaseModel):
    selected_image_index: int = Field(description="Index (0-based) of the best image that clearly shows the full object from front view")
    reasoning: str = Field(description="Brief explanation of why this image was selected")

llm = ChatOpenAI(
    # model="gpt-4.1",
    model="gpt-4.1-mini",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    api_key=api_key,
)

# Create structured LLMs
structured_llm = llm.with_structured_output(ItemDescription)
image_selection_llm = llm.with_structured_output(ImageSelection)

def load_and_encode_image(image_path):
    """Load an image and convert it to base64"""
    try:
        # Open and convert image to RGB (in case it's in a different format)
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # Save to bytes
            import io
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format='JPEG')
            img_byte_arr = img_byte_arr.getvalue()

            # Encode to base64
            image_base64 = base64.b64encode(img_byte_arr).decode('utf-8')
            return image_base64
    except Exception as e:
        print(f"Error loading image: {e}")
        return None

def select_best_image_for_similarity(images_base64: List[str]) -> dict:
    """
    Select the best image from multiple images for similarity search.
    Chooses the image that clearly shows the full object from front view.

    Args:
        images_base64 (List[str]): List of base64 encoded image strings

    Returns:
        dict: Dictionary with selected_image_index, reasoning, and selected_image_base64
    """
    if not images_base64:
        return {"selected_image_index": 0, "reasoning": "No images provided", "selected_image_base64": None}

    if len(images_base64) == 1:
        return {
            "selected_image_index": 0,
            "reasoning": "Only one image provided",
            "selected_image_base64": images_base64[0]
        }

    # Create system prompt for image selection
    system_prompt = """You are an expert at analyzing images for catalog similarity search.
    Your task is to select the BEST image from multiple images of the same object for similarity matching.

    Selection criteria (in order of importance):
    1. Shows the FULL object (not cropped or partially visible)
    2. Front-facing view (not side, back, or angled views)
    3. Clear and well-lit (not blurry, dark, or overexposed)
    4. Object is the main focus (not a detail shot or close-up of a part)
    5. Neutral background (object stands out clearly)
    6. Standard viewing angle (not top-down or bottom-up views)

    Choose the image that would be most useful for finding similar items in a catalog database."""

    # Create messages with all images
    message_content = [
        {
            "type": "text",
            "text": f"Please analyze these {len(images_base64)} images and select the best one for similarity search. Consider which image shows the full object most clearly from a front-facing view."
        }
    ]

    # Add all images to the message
    for i, img_base64 in enumerate(images_base64):
        message_content.append({
            "type": "text",
            "text": f"Image {i+1}:"
        })
        message_content.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{img_base64}"
            }
        })

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=message_content)
    ]

    try:
        response = image_selection_llm.invoke(messages)

        # Validate the selected index
        selected_index = response.selected_image_index
        if selected_index < 0 or selected_index >= len(images_base64):
            selected_index = 0  # Default to first image if invalid

        return {
            "selected_image_index": selected_index,
            "reasoning": response.reasoning,
            "selected_image_base64": images_base64[selected_index]
        }

    except Exception as e:
        print(f"Error selecting best image: {e}")
        # Default to first image if selection fails
        return {
            "selected_image_index": 0,
            "reasoning": f"Error in selection process: {str(e)}. Defaulted to first image.",
            "selected_image_base64": images_base64[0]
        }

def create_system_prompt_with_examples(examples):
    """Create system prompt with examples"""
    examples_text = "\n\n".join([
        f"Title: {ex['title']}\nDescription: {ex['description']}"
        for ex in examples
    ])

    system_prompt = f"""You are an expert antique and furniture analyst specializing in auction catalog descriptions.

    Please analyze the provided image and create a title and description following the exact style and format of these examples:

    {examples_text}

    Key style guidelines from the examples:
    - Titles should be concise, descriptive, and mention the main type of furniture and key distinguishing features
    - Descriptions should start with "This [item type] features..."
    - Include specific details about construction, materials, design elements, and condition
    - Mention any notable damage, wear, or distinguishing marks
    - Use professional, formal language appropriate for auction catalogs
    - Focus on factual, objective descriptions"""

    return system_prompt


def generate_new_catalog_item(images_base64, examples):
    """
    Analyze images using provided examples for style guidance

    Args:
        images_base64 (str or List[str]): Base64 encoded image string(s)
        examples (list): List of dictionaries with 'title' and 'description' keys

    Returns:
        ItemDescription: Pydantic object with title and description, or None if error
    """
    # Handle both single image (backward compatibility) and multiple images
    if isinstance(images_base64, str):
        images_base64 = [images_base64]

    if not images_base64:
        print("No image data provided.")
        return None

    # Create messages with system prompt and images
    system_prompt = create_system_prompt_with_examples(examples)

    # Create message content starting with text instruction
    message_content = [
        {
            "type": "text",
            "text": f"Please analyze these {len(images_base64)} image(s) and provide a title and description following the style guidelines specified. Consider all images to create the most comprehensive and accurate description."
        }
    ]

    # Add all images to the message
    for i, img_base64 in enumerate(images_base64):
        if len(images_base64) > 1:
            message_content.append({
                "type": "text",
                "text": f"Image {i+1}:"
            })
        message_content.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{img_base64}"
            }
        })

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=message_content)
    ]

    # Get structured response from the LLM
    try:
        response = structured_llm.invoke(messages)
        return response.model_dump()

    except Exception as e:
        print(f"Error getting response from LLM: {e}")
        return None

def agent_generator(images_base64):
    """
    Generate catalog item description from single or multiple images

    Args:
        images_base64 (str or List[str]): Base64 encoded image string(s)

    Returns:
        dict: Dictionary with title, description, and optionally selected_image_info
    """
    # Handle both single image (backward compatibility) and multiple images
    if isinstance(images_base64, str):
        images_base64 = [images_base64]
        is_single_image = True
    else:
        is_single_image = False

    collection = client.get_or_create_collection(
        name="catalog_images",
        metadata={"hnsw:space": "cosine"}
    )

    # Get all data from collection
    results = collection.get()
    print(f"Total items: {len(results['ids'])}")

    # For multiple images, select the best one for similarity search
    selected_image_info = None
    if len(images_base64) > 1:
        selection_result = select_best_image_for_similarity(images_base64)
        selected_image_base64 = selection_result["selected_image_base64"]
        selected_image_info = {
            "selected_image_index": selection_result["selected_image_index"],
            "reasoning": selection_result["reasoning"]
        }
        print(f"Selected image {selection_result['selected_image_index'] + 1} for similarity search: {selection_result['reasoning']}")
    else:
        selected_image_base64 = images_base64[0]
        print("Using single image for similarity search")

    # Search for similar items using the selected image
    similar_items = search_similar_images_from_base64(selected_image_base64, collection, k=20)
    examples = [{'title': item['title'], 'description': item['description']} for item in similar_items]

    # Generate catalog item using all images
    result = generate_new_catalog_item(images_base64, examples)

    if result is not None:
        # Add selected image info for multiple images
        if selected_image_info and not is_single_image:
            result['selected_image_info'] = selected_image_info
        return result
    else:
        print("Error generating catalog item.")
        error_result = {'title': 'Error generating catalog item.', 'description': 'Error generating catalog item.'}
        if selected_image_info and not is_single_image:
            error_result['selected_image_info'] = selected_image_info
        return error_result