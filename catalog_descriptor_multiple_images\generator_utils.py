import torch
from PIL import Image
import numpy as np
import os
import io 
import base64

device = "cuda" if torch.cuda.is_available() else "cpu"



def base64_to_pil_image(image_base64):
    """
    Convert base64 string to PIL Image
    
    Args:
        image_base64 (str): Base64 encoded image string
    
    Returns:
        PIL.Image: PIL Image object
    """
    # Decode base64 string
    image_data = base64.b64decode(image_base64)
    
    # Convert to PIL Image
    image = Image.open(io.BytesIO(image_data))
    
    return image

from transformers import CLIPProcessor, CLIPModel

# Load model and processor
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32").to(device)
processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

def get_image_embedding(pil_image):
    """
    Get image embedding using Hugging Face transformers
    
    Args:
        pil_image (PIL.Image): PIL Image object
    
    Returns:
        numpy.ndarray: Image embedding vector
    """

    
    # Process image
    inputs = processor(images=pil_image, return_tensors="pt").to(device)  # Move inputs to device

    # Get image features
    with torch.no_grad():
        image_features = model.get_image_features(**inputs)
        # Normalize
        image_features = image_features / image_features.norm(dim=-1, keepdim=True)

    return image_features.cpu().numpy().flatten()  # Move back to CPU for numpy

import pandas as pd
import chromadb
from chromadb.config import Settings

# Setup ChromaDB
VECTOR_DIR = "./chroma_db"
settings = Settings(
    is_persistent=True,
    persist_directory=VECTOR_DIR,
    anonymized_telemetry=False,
)

client = chromadb.PersistentClient(settings=settings)


def search_similar_images_from_base64(image_base64, collection, k=5):
    """
    Search for similar images using base64 encoded image
    
    Args:
        image_base64 (str): Base64 encoded image string
        k (int): Number of similar images to return
    
    Returns:
        list: List of dictionaries with similar items
    """
    # Convert base64 to PIL image
    pil_image = base64_to_pil_image(image_base64)
    
    # Get query embedding
    query_embedding = get_image_embedding(pil_image)
    
    # Search
    results = collection.query(
        query_embeddings=[query_embedding.tolist()],
        n_results=k
    )
    
    # Convert to list of dicts with CSV structure
    similar_items = []
    for i, metadata in enumerate(results['metadatas'][0]):
        similar_items.append({
            'item_number': metadata['item_number'],
            'title': metadata['title'],
            'description': metadata['description'],
            'page': metadata['page'],
            'image_path': metadata['image_path'],
            'similarity_score': results['distances'][0][i]
        })
    
    return similar_items


from langchain_openai import ChatOpenAI
import os
import base64
from PIL import Image
from langchain.schema import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import Optional

from dotenv import load_dotenv
load_dotenv()
api_key = os.getenv('OPENAI_API_KEY')


# Pydantic model for structured output
class ItemDescription(BaseModel):
    title: str = Field(description="Concise, descriptive title mentioning the main type of furniture and key distinguishing features")
    description: str = Field(description="Detailed description following auction catalog style, starting with 'This [item type] features...'")

llm = ChatOpenAI(
    # model="gpt-4.1",
    model="gpt-4.1-mini",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    api_key=api_key,  
)

# Create structured LLM
structured_llm = llm.with_structured_output(ItemDescription)

def load_and_encode_image(image_path):
    """Load an image and convert it to base64"""
    try:
        # Open and convert image to RGB (in case it's in a different format)
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # Save to bytes
            import io
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format='JPEG')
            img_byte_arr = img_byte_arr.getvalue()

            # Encode to base64
            image_base64 = base64.b64encode(img_byte_arr).decode('utf-8')
            return image_base64
    except Exception as e:
        print(f"Error loading image: {e}")
        return None

def create_system_prompt_with_examples(examples):
    """Create system prompt with examples"""
    examples_text = "\n\n".join([
        f"Title: {ex['title']}\nDescription: {ex['description']}" 
        for ex in examples
    ])
    
    system_prompt = f"""You are an expert antique and furniture analyst specializing in auction catalog descriptions. 

    Please analyze the provided image and create a title and description following the exact style and format of these examples:

    {examples_text}

    Key style guidelines from the examples:
    - Titles should be concise, descriptive, and mention the main type of furniture and key distinguishing features
    - Descriptions should start with "This [item type] features..." 
    - Include specific details about construction, materials, design elements, and condition
    - Mention any notable damage, wear, or distinguishing marks
    - Use professional, formal language appropriate for auction catalogs
    - Focus on factual, objective descriptions"""
    
    return system_prompt


def generate_new_catalog_item(image_base64, examples):
    """
    Analyze an image using provided examples for style guidance
    
    Args:
        image_base64 (str): Base64 encoded image string
        examples (list): List of dictionaries with 'title' and 'description' keys
    
    Returns:
        ItemDescription: Pydantic object with title and description, or None if error
    """
    if not image_base64:
        print("No image data provided.")
        return None
        
    # Create messages with system prompt and image
    system_prompt = create_system_prompt_with_examples(examples)
    
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=[
            {
                "type": "text",
                "text": "Please analyze this image and provide a title and description following the style guidelines specified."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{image_base64}"
                }
            }
        ])
    ]

    # Get structured response from the LLM
    try:
        response = structured_llm.invoke(messages)
        # print("Structured Output:")
        # print("-" * 50)
        # print(f"Title: {response.title}")
        # print(f"Description: {response.description}")
        
        # You can also access the full Pydantic object
        # print(f"\nFull object: {response}")
        
        return response.model_dump()
        
    except Exception as e:
        print(f"Error getting response from LLM: {e}")
        return None

def agent_generator(image_base64):
    collection = client.get_or_create_collection(
        name="catalog_images",
        metadata={"hnsw:space": "cosine"}
    )

    # Get all data from collection
    results = collection.get()

    # Print count
    print(f"Total items: {len(results['ids'])}")

    similar_items = search_similar_images_from_base64(image_base64,collection, k=20)
    examples = [{'title': item['title'], 'description': item['description']} for item in similar_items]
    for exp in examples:
        print(exp)
    result = generate_new_catalog_item(image_base64, examples)
    # result = {
    #     "title": "test",
    #     "description": "test"
    # }
    if result is not None:
        return result
    else:
        print("Error generating catalog item.")
        return {'title': 'Error generating catalog item.', 'description': 'Error generating catalog item.'}