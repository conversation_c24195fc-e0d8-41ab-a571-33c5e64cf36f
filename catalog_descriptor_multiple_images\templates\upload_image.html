<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload - Catalog Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #2d3748;
        }

        .upload-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
            padding: 48px;
            margin-top: 30px;
            border: 1px solid #e2e8f0;
        }

        @media (max-width: 768px) {
            .upload-container {
                padding: 32px 24px;
                margin-top: 20px;
                border-radius: 12px;
            }

            .drop-zone {
                padding: 48px 16px;
            }

            .display-5 {
                font-size: 2rem;
            }
        }

        .drop-zone {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 64px 24px;
            text-align: center;
            transition: all 0.2s ease;
            background-color: #f7fafc;
            cursor: pointer;
            position: relative;
        }

        .drop-zone:hover {
            border-color: #38a169;
            background-color: #f0fff4;
        }

        .drop-zone.dragover {
            border-color: #38a169;
            background-color: #f0fff4;
            border-style: solid;
        }

        .upload-icon {
            font-size: 3.5rem;
            color: #a0aec0;
            margin-bottom: 24px;
            transition: color 0.2s ease;
        }

        .drop-zone:hover .upload-icon {
            color: #38a169;
        }

        .drop-zone.dragover .upload-icon {
            color: #38a169;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background-color: #38a169;
            border: none;
            border-radius: 8px;
            padding: 14px 32px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            color: white;
        }

        .upload-btn:hover {
            background-color: #2f855a;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
        }

        .upload-btn:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-info {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
            display: none;
        }

        .progress {
            height: 6px;
            border-radius: 3px;
            margin: 24px 0;
            display: none;
            background-color: #e2e8f0;
        }

        .progress-bar {
            background-color: #38a169;
        }

        .alert {
            border-radius: 8px;
            border: 1px solid;
            margin-top: 24px;
            padding: 16px 20px;
        }

        .alert-success {
            background-color: #f0fff4;
            border-color: #9ae6b4;
            color: #22543d;
        }

        .alert-danger {
            background-color: #fed7d7;
            border-color: #fc8181;
            color: #742a2a;
        }

        .alert-warning {
            background-color: #fefcbf;
            border-color: #f6e05e;
            color: #744210;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        .btn-outline-success {
            border-color: #38a169;
            color: #38a169;
            background-color: transparent;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-outline-success:hover {
            background-color: #38a169;
            border-color: #38a169;
            color: white;
        }

        .btn-outline-danger {
            border-color: #e53e3e;
            color: #e53e3e;
            background-color: transparent;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-outline-danger:hover {
            background-color: #e53e3e;
            border-color: #e53e3e;
            color: white;
        }

        .text-success {
            color: #2f855a !important;
        }

        .display-5 {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1.2;
        }

        .lead {
            font-size: 1.125rem;
            font-weight: 400;
        }

        h4 {
            color: #2d3748;
            font-weight: 600;
        }

        .text-muted {
            color: #718096 !important;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 16px;
            border: 1px solid #e2e8f0;
        }

        .result-container {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            margin-top: 24px;
            display: none;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="upload-container">
                    <div class="text-center mb-5">
                        <div class="mb-4">
                            <i class="fas fa-image text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h1 class="display-5 fw-bold text-success mb-3">
                            Image Catalog Generator
                        </h1>
                        <p class="lead text-muted mb-0">Upload your image to generate catalog description</p>
                        <p class="text-muted" style="font-size: 0.9rem;">Supports JPG, PNG, WebP files up to 5MB</p>
                    </div>

                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="drop-zone" id="dropZone">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h4 class="mb-3">Drop your image here</h4>
                            <p class="text-muted mb-4">or click the button below to browse</p>
                            <input type="file" id="fileInput" name="file" class="file-input" accept=".jpg,.jpeg,.png,.webp">
                            <button type="button" class="btn btn-outline-success" id="chooseFileBtn">
                                <i class="fas fa-folder-open me-2"></i>Choose Image
                            </button>
                            <div class="mt-3">
                                <small class="text-muted">JPG, PNG, WebP files only</small>
                            </div>
                        </div>

                        <div class="file-info" id="fileInfo">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    <i class="fas fa-image" style="color: #38a169; font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold" id="fileName" style="color: #2d3748;"></h6>
                                    <small class="text-muted" id="fileSize"></small>
                                    <img id="imagePreview" class="image-preview" style="display: none;" />
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()" title="Remove file">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="progress" id="progressBar">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>

                        <div class="text-center mt-5">
                            <button type="submit" class="btn upload-btn" id="uploadBtn" disabled>
                                <span id="uploadText">
                                    <i class="fas fa-magic me-2"></i>Generate Description
                                </span>
                                <span id="uploadSpinner" style="display: none;">
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </form>

                    <div id="result"></div>

                    <div class="result-container" id="resultContainer">
                        <h5 class="text-success mb-3"><i class="fas fa-check-circle me-2"></i>Generated Description</h5>
                        <div class="mb-3">
                            <strong>Title:</strong>
                            <p id="resultTitle" class="mb-2"></p>
                        </div>
                        <div>
                            <strong>Description:</strong>
                            <p id="resultDescription" class="mb-0"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const imagePreview = document.getElementById('imagePreview');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadForm = document.getElementById('uploadForm');
        const progressBar = document.getElementById('progressBar');
        const result = document.getElementById('result');
        const uploadText = document.getElementById('uploadText');
        const uploadSpinner = document.getElementById('uploadSpinner');
        const resultContainer = document.getElementById('resultContainer');
        const resultTitle = document.getElementById('resultTitle');
        const resultDescription = document.getElementById('resultDescription');

        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

        dropZone.addEventListener('click', () => fileInput.click());

        // Handle button click separately to prevent double opening
        document.getElementById('chooseFileBtn').addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent bubbling to dropZone
            fileInput.click();
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!allowedTypes.includes(file.type)) {
                showAlert('Please select a valid image file (JPG, PNG, WebP).', 'danger');
                return;
            }


            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);

            // Show image preview
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            fileInfo.style.display = 'block';
            uploadBtn.disabled = false;

            result.innerHTML = '';
            resultContainer.style.display = 'none';
        }

        function clearFile() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            imagePreview.style.display = 'none';
            uploadBtn.disabled = true;
            progressBar.style.display = 'none';
            result.innerHTML = '';
            resultContainer.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showAlert(message, type) {
            result.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!fileInput.files[0]) {
                showAlert('Please select a file first.', 'warning');
                return;
            }

            // Convert image to base64
            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = async function(e) {
                const base64String = e.target.result.split(',')[1]; // Remove data:image/...;base64, prefix

                uploadBtn.disabled = true;
                uploadText.style.display = 'none';
                uploadSpinner.style.display = 'inline';
                progressBar.style.display = 'block';

                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressBar.querySelector('.progress-bar').style.width = progress + '%';
                }, 200);

                try {
                    const response = await fetch('/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            image_base64: base64String
                        })
                    });

                    const data = await response.json();

                    clearInterval(progressInterval);
                    progressBar.querySelector('.progress-bar').style.width = '100%';

                    setTimeout(() => {
                        progressBar.style.display = 'none';

                        if (data.status === 'success') {
                            showAlert('Image processed successfully!', 'success');

                            // Show results
                            resultTitle.textContent = data.data.title;
                            resultDescription.textContent = data.data.description;
                            resultContainer.style.display = 'block';
                        } else {
                            showAlert(data.message || 'Processing failed', 'danger');
                        }

                        uploadBtn.disabled = false;
                        uploadText.style.display = 'inline';
                        uploadSpinner.style.display = 'none';
                    }, 500);

                } catch (error) {
                    clearInterval(progressInterval);
                    progressBar.style.display = 'none';
                    showAlert('Network error occurred. Please try again.', 'danger');

                    uploadBtn.disabled = false;
                    uploadText.style.display = 'inline';
                    uploadSpinner.style.display = 'none';
                }
            };

            reader.readAsDataURL(file);
        });
    </script>
</body>
</html>