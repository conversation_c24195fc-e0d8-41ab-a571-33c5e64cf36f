<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload - Catalog Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #2d3748;
        }

        .upload-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
            padding: 48px;
            margin-top: 30px;
            border: 1px solid #e2e8f0;
        }

        @media (max-width: 768px) {
            .upload-container {
                padding: 32px 24px;
                margin-top: 20px;
                border-radius: 12px;
            }

            .drop-zone {
                padding: 48px 16px;
            }

            .display-5 {
                font-size: 2rem;
            }
        }

        .drop-zone {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 64px 24px;
            text-align: center;
            transition: all 0.2s ease;
            background-color: #f7fafc;
            cursor: pointer;
            position: relative;
        }

        .drop-zone:hover {
            border-color: #38a169;
            background-color: #f0fff4;
        }

        .drop-zone.dragover {
            border-color: #38a169;
            background-color: #f0fff4;
            border-style: solid;
        }

        .upload-icon {
            font-size: 3.5rem;
            color: #a0aec0;
            margin-bottom: 24px;
            transition: color 0.2s ease;
        }

        .drop-zone:hover .upload-icon {
            color: #38a169;
        }

        .drop-zone.dragover .upload-icon {
            color: #38a169;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background-color: #38a169;
            border: none;
            border-radius: 8px;
            padding: 14px 32px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            color: white;
        }

        .upload-btn:hover {
            background-color: #2f855a;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
        }

        .upload-btn:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-info {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
            display: none;
        }

        .progress {
            height: 6px;
            border-radius: 3px;
            margin: 24px 0;
            display: none;
            background-color: #e2e8f0;
        }

        .progress-bar {
            background-color: #38a169;
        }

        .alert {
            border-radius: 8px;
            border: 1px solid;
            margin-top: 24px;
            padding: 16px 20px;
        }

        .alert-success {
            background-color: #f0fff4;
            border-color: #9ae6b4;
            color: #22543d;
        }

        .alert-danger {
            background-color: #fed7d7;
            border-color: #fc8181;
            color: #742a2a;
        }

        .alert-warning {
            background-color: #fefcbf;
            border-color: #f6e05e;
            color: #744210;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        .btn-outline-success {
            border-color: #38a169;
            color: #38a169;
            background-color: transparent;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-outline-success:hover {
            background-color: #38a169;
            border-color: #38a169;
            color: white;
        }

        .btn-outline-danger {
            border-color: #e53e3e;
            color: #e53e3e;
            background-color: transparent;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-outline-danger:hover {
            background-color: #e53e3e;
            border-color: #e53e3e;
            color: white;
        }

        .text-success {
            color: #2f855a !important;
        }

        .display-5 {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1.2;
        }

        .lead {
            font-size: 1.125rem;
            font-weight: 400;
        }

        h4 {
            color: #2d3748;
            font-weight: 600;
        }

        .text-muted {
            color: #718096 !important;
        }

        .image-preview {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            margin: 8px;
            border: 1px solid #e2e8f0;
            object-fit: cover;
        }

        .images-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 16px;
        }

        .image-item {
            position: relative;
            margin: 8px;
        }

        .remove-image-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #e53e3e;
            color: white;
            border: none;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-image-btn:hover {
            background-color: #c53030;
        }

        .result-container {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            margin-top: 24px;
            display: none;
        }
    </style>
</head>
<body>
    {% include 'navbar.html' %}

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="upload-container">
                    <div class="text-center mb-5">
                        <div class="mb-4">
                            <i class="fas fa-image text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h1 class="display-5 fw-bold text-success mb-3">
                            Image Catalog Generator
                        </h1>
                        <p class="lead text-muted mb-0">Upload your images to generate catalog description</p>
                        <p class="text-muted" style="font-size: 0.9rem;">Supports JPG, PNG, WebP files up to 5MB each. Multiple images supported.</p>
                    </div>

                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="drop-zone" id="dropZone">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h4 class="mb-3">Drop your images here</h4>
                            <p class="text-muted mb-4">or click the button below to browse</p>
                            <input type="file" id="fileInput" name="file" class="file-input" accept=".jpg,.jpeg,.png,.webp" multiple>
                            <button type="button" class="btn btn-outline-success" id="chooseFileBtn">
                                <i class="fas fa-folder-open me-2"></i>Choose Images
                            </button>
                            <div class="mt-3">
                                <small class="text-muted">JPG, PNG, WebP files only. Multiple images supported.</small>
                            </div>
                        </div>

                        <div class="file-info" id="fileInfo">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    <i class="fas fa-images" style="color: #38a169; font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold" id="fileCount" style="color: #2d3748;"></h6>
                                    <small class="text-muted" id="totalSize"></small>
                                    <div class="images-container" id="imagesContainer"></div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFiles()" title="Remove all files">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="progress" id="progressBar">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>

                        <div class="text-center mt-5">
                            <button type="submit" class="btn upload-btn" id="uploadBtn" disabled>
                                <span id="uploadText">
                                    <i class="fas fa-magic me-2"></i>Generate Description
                                </span>
                                <span id="uploadSpinner" style="display: none;">
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </form>

                    <div id="result"></div>

                    <div class="result-container" id="resultContainer">
                        <h5 class="text-success mb-3"><i class="fas fa-check-circle me-2"></i>Generated Description</h5>

                        <div class="mb-3" id="selectedImageContainer" style="display: none;">
                            <strong>Selected Image for Analysis:</strong>
                            <p id="selectedImageReason" class="text-muted small mb-2"></p>
                            <img id="selectedImagePreview" class="image-preview" style="max-width: 200px; max-height: 200px;" />
                        </div>

                        <div class="mb-3">
                            <strong>Title:</strong>
                            <p id="resultTitle" class="mb-2"></p>
                        </div>
                        <div>
                            <strong>Description:</strong>
                            <p id="resultDescription" class="mb-0"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileCount = document.getElementById('fileCount');
        const totalSize = document.getElementById('totalSize');
        const imagesContainer = document.getElementById('imagesContainer');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadForm = document.getElementById('uploadForm');
        const progressBar = document.getElementById('progressBar');
        const result = document.getElementById('result');
        const uploadText = document.getElementById('uploadText');
        const uploadSpinner = document.getElementById('uploadSpinner');
        const resultContainer = document.getElementById('resultContainer');
        const resultTitle = document.getElementById('resultTitle');
        const resultDescription = document.getElementById('resultDescription');
        const selectedImageContainer = document.getElementById('selectedImageContainer');
        const selectedImageReason = document.getElementById('selectedImageReason');
        const selectedImagePreview = document.getElementById('selectedImagePreview');

        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        let selectedFiles = [];

        dropZone.addEventListener('click', () => fileInput.click());

        // Handle button click separately to prevent double opening
        document.getElementById('chooseFileBtn').addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent bubbling to dropZone
            fileInput.click();
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        function handleFiles(files) {
            const validFiles = files.filter(file => {
                if (!allowedTypes.includes(file.type)) {
                    showAlert(`File ${file.name} is not a valid image type. Please select JPG, PNG, or WebP files.`, 'warning');
                    return false;
                }
                return true;
            });

            if (validFiles.length === 0) {
                return;
            }

            // Add new files to selected files
            selectedFiles = [...selectedFiles, ...validFiles];
            updateFileDisplay();

            uploadBtn.disabled = false;
            result.innerHTML = '';
            resultContainer.style.display = 'none';
        }

        function updateFileDisplay() {
            if (selectedFiles.length === 0) {
                fileInfo.style.display = 'none';
                uploadBtn.disabled = true;
                return;
            }

            fileCount.textContent = `${selectedFiles.length} image${selectedFiles.length > 1 ? 's' : ''} selected`;

            const totalBytes = selectedFiles.reduce((sum, file) => sum + file.size, 0);
            totalSize.textContent = formatFileSize(totalBytes);

            // Clear and rebuild images container
            imagesContainer.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';

                const img = document.createElement('img');
                img.className = 'image-preview';

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-image-btn';
                removeBtn.innerHTML = '×';
                removeBtn.onclick = () => removeImage(index);

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);

                imageItem.appendChild(img);
                imageItem.appendChild(removeBtn);
                imagesContainer.appendChild(imageItem);
            });

            fileInfo.style.display = 'block';
        }

        function removeImage(index) {
            selectedFiles.splice(index, 1);
            updateFileDisplay();

            if (selectedFiles.length === 0) {
                clearFiles();
            }
        }

        function clearFiles() {
            fileInput.value = '';
            selectedFiles = [];
            fileInfo.style.display = 'none';
            uploadBtn.disabled = true;
            progressBar.style.display = 'none';
            result.innerHTML = '';
            resultContainer.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showAlert(message, type) {
            result.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (selectedFiles.length === 0) {
                showAlert('Please select at least one image first.', 'warning');
                return;
            }

            // Convert all images to base64
            const base64Images = [];
            const conversionPromises = selectedFiles.map(file => {
                return new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const base64String = e.target.result.split(',')[1]; // Remove data:image/...;base64, prefix
                        resolve(base64String);
                    };
                    reader.readAsDataURL(file);
                });
            });

            try {
                const base64Results = await Promise.all(conversionPromises);

                uploadBtn.disabled = true;
                uploadText.style.display = 'none';
                uploadSpinner.style.display = 'inline';
                progressBar.style.display = 'block';

                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressBar.querySelector('.progress-bar').style.width = progress + '%';
                }, 200);

                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        images_base64: base64Results
                    })
                });

                const data = await response.json();

                clearInterval(progressInterval);
                progressBar.querySelector('.progress-bar').style.width = '100%';

                setTimeout(() => {
                    progressBar.style.display = 'none';

                    if (data.status === 'success') {
                        const imageText = selectedFiles.length > 1 ? 'Images' : 'Image';
                        showAlert(`${imageText} processed successfully!`, 'success');

                        // Show results
                        resultTitle.textContent = data.data.title;
                        resultDescription.textContent = data.data.description;

                        // Show selected image info if multiple images were uploaded
                        if (data.data.selected_image_info && selectedFiles.length > 1) {
                            const selectedIndex = data.data.selected_image_info.selected_image_index;
                            selectedImageReason.textContent = `Image ${selectedIndex + 1} was selected: ${data.data.selected_image_info.reasoning}`;

                            // Show the selected image
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                selectedImagePreview.src = e.target.result;
                            };
                            reader.readAsDataURL(selectedFiles[selectedIndex]);

                            selectedImageContainer.style.display = 'block';
                        } else {
                            selectedImageContainer.style.display = 'none';
                        }

                        resultContainer.style.display = 'block';
                    } else {
                        showAlert(data.message || 'Processing failed', 'danger');
                    }

                    uploadBtn.disabled = false;
                    uploadText.style.display = 'inline';
                    uploadSpinner.style.display = 'none';
                }, 500);

            } catch (error) {
                if (typeof progressInterval !== 'undefined') {
                    clearInterval(progressInterval);
                }
                progressBar.style.display = 'none';
                showAlert('Network error occurred. Please try again.', 'danger');

                uploadBtn.disabled = false;
                uploadText.style.display = 'inline';
                uploadSpinner.style.display = 'none';
            }
        });
    </script>
</body>
</html>