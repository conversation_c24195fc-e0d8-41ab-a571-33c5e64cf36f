# Flask Catalog Generator

A Flask web application with two POST endpoints for catalog generation and PDF upload functionality.

## Features

- **Upload Catalog Endpoint** (`/upload_catalog`): Accepts base64 image data and returns catalog information
- **Generate Endpoint** (`/generate`): Accepts PDF file uploads through a web interface
- **Web Interface**: Beautiful Bootstrap-styled upload page with drag-and-drop functionality

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python app.py
```

3. Open your browser and navigate to `http://localhost:5000`

## API Endpoints

### POST /upload_catalog
Accepts JSON data with base64 image and calls the `agent_generator` method.

**Request:**
```json
{
    "image_base64": "base64_encoded_image_data"
}
```

**Response:**
```json
{
    "status": "success",
    "data": {
        "title": "test",
        "description": "test"
    }
}
```

### POST /generate
Accepts PDF file uploads and processes them using the `upload_method`.

**Request:** Multipart form data with file field named "file"

**Response:**
```json
{
    "status": "success",
    "message": "PDF uploaded and processed successfully",
    "filename": "example.pdf",
    "file_size": 1024
}
```

## Web Interface

The application includes a responsive web interface at the root URL (`/`) that provides:

- Drag and drop PDF upload
- File validation (PDF only, max 16MB)
- Progress indication
- Bootstrap styling with modern UI/UX
- Real-time feedback and error handling

## File Structure

```
catalog_descriptor/
├── app.py                 # Main Flask application
├── templates/
│   └── upload.html       # Web interface template
├── uploads/              # Directory for uploaded files (created automatically)
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Customization

- Modify the `agent_generator()` function in `app.py` to implement your actual catalog generation logic
- Update the `upload_method()` function to add your PDF processing logic
- Customize the web interface by editing `templates/upload.html`
