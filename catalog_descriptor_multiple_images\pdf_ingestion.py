
import fitz  # PyMuPDF
import io
from PIL import Image
import os
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import re
import base64
import uuid


import pandas as pd
import chromadb
from chromadb.config import Settings
import numpy as np
import os

# Setup ChromaDB
VECTOR_DIR = "./chroma_db"
settings = Settings(
    is_persistent=True,
    persist_directory=VECTOR_DIR,
    anonymized_telemetry=False,
)

client = chromadb.PersistentClient(settings=settings)


def encode_pdf_to_base64(pdf_path: str) -> str:
    """
    Encode a PDF file to base64 string.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        str: Base64 encoded PDF data
    """
    try:
        with open(pdf_path, 'rb') as pdf_file:
            pdf_data = pdf_file.read()
            pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')
            return pdf_base64
    except Exception as e:
        print(f"Error encoding PDF to base64: {e}")
        return None

def extract_catalog_items(pdf_base64: str, output_dir: str = "catalog_images") -> list:
    """
    Extract catalog items with their corresponding text and images from base64 encoded PDF.
    
    Args:
        pdf_base64 (str): Base64 encoded PDF data
        output_dir (str): Directory to save extracted images
        
    Returns:
        list: List of catalog items with text and image
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Decode base64 to bytes
        pdf_data = base64.b64decode(pdf_base64)
        
        # Open PDF from bytes
        doc = fitz.open(stream=pdf_data, filetype="pdf")
        
    except Exception as e:
        print(f"Error decoding PDF from base64: {e}")
        return []
    
    catalog_items = []
    
    print(f"📄 Processing PDF from base64 data")
    print(f"📑 Total pages: {len(doc)}")
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        
        # Extract text from page
        text = page.get_text().strip()
        
        # Extract images from page
        image_list = page.get_images()
        
        # Extract images and save them
        page_images = []
        for img_index, img in enumerate(image_list):
            try:
                # Get image data
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                # Convert to PIL Image
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("ppm")
                    img_pil = Image.open(io.BytesIO(img_data))
                else:  # CMYK
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    img_data = pix1.tobytes("ppm")
                    img_pil = Image.open(io.BytesIO(img_data))
                    pix1 = None
                
                print(f"🖼️ Image {img_index+1} on page {page_num+1}: {img_pil.size[0]}x{img_pil.size[1]} pixels")
                
                # Save image with unique ID
                unique_id = str(uuid.uuid4())
                img_filename = f"{unique_id}.png"
                img_path = os.path.join(output_dir, img_filename)
                img_pil.save(img_path)
                
                page_images.append(img_path)
                pix = None
                
            except Exception as e:
                print(f"❌ Error extracting image {img_index+1} from page {page_num+1}: {e}")
        
        # Parse catalog items from text
        items = parse_items_from_text(text, page_num + 1, page_images)
        catalog_items.extend(items)
    
    doc.close()
    
    print(f"✅ Extracted {len(catalog_items)} catalog items")
    
    return catalog_items

def parse_items_from_text(text: str, page_num: int, page_images: list) -> list:
    """
    Parse individual catalog items from page text into item_number, title, and description.
    
    Args:
        text (str): Raw text from PDF page
        page_num (int): Page number
        page_images (list): List of image paths for this page
        
    Returns:
        list: List of catalog items with item_number, title, description
    """
    items = []
    
    # Split text into lines
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # Find item numbers
    current_item = None
    current_text = []
    image_index = 0
    
    for line in lines:
        # Check if line is just a number (item number)
        number_pattern = r"^\d+$"
        if re.match(number_pattern, line.strip()) and len(line.strip()) <= 3:
            # Save previous item if exists
            if current_item is not None:
                # Parse title and description from collected text
                parse_title_and_description(current_item, current_text)
                items.append(current_item)
            
            # Start new item
            item_number = line.strip()
            current_item = {
                'item_number': item_number,
                'title': '',
                'description': '',
                'page': page_num,
                'image_path': page_images[image_index] if image_index < len(page_images) else None
            }
            current_text = []
            image_index += 1
            
        elif current_item is not None:
            # Add line to current item's text
            current_text.append(line)
    
    # Don't forget the last item
    if current_item is not None:
        parse_title_and_description(current_item, current_text)
        items.append(current_item)
    
    return items

def parse_title_and_description(item: dict, text_lines: list):
    """
    Parse title and description from text lines.
    
    Args:
        item (dict): Current item dictionary to update
        text_lines (list): List of text lines for this item
    """
    if not text_lines:
        return
    
    # First line is typically the title
    item['title'] = text_lines[0] if text_lines else ''
    
    # Everything else is the description
    if len(text_lines) > 1:
        item['description'] = ' '.join(text_lines[1:])
    else:
        item['description'] = ''

def display_items(catalog_items: list):
    """
    Display catalog items one by one with item number, title, description and image.
    
    Args:
        catalog_items (list): List of catalog items to display
    """
    
    print(f"\n{'='*60}")
    print(f"📦 DISPLAYING {len(catalog_items)} CATALOG ITEMS")
    print(f"{'='*60}")
    
    for i, item in enumerate(catalog_items):
        print(f"\n🔸 ITEM {item['item_number']} (Page {item['page']}):")
        print(f"📌 Title: {item['title']}")
        print(f"📄 Description: {item['description']}")
        
        # Display image for this item
        img_path = item['image_path']
        if img_path and os.path.exists(img_path):
            try:
                # Create individual plot for this item
                fig, ax = plt.subplots(1, 1, figsize=(8, 6))
                img = mpimg.imread(img_path)
                ax.imshow(img)
                ax.set_title(f"Item {item['item_number']}: {item['title']}", fontsize=12, wrap=True)
                ax.axis('off')
                plt.tight_layout()
                plt.show()
                print(f"🖼️ Image: {img_path}")
            except Exception as e:
                print(f"❌ Error loading image: {e}")
        else:
            print("🖼️ No image available")
        
        print("-" * 60)
    
    print(f"\n✅ Displayed {len(catalog_items)} items")


import torch
from transformers import CLIPProcessor, CLIPModel

device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32").to(device)
processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

def get_image_embedding(image_path):
    """Get image embedding using Hugging Face transformers"""


    image = Image.open(image_path)
    inputs = processor(images=image, return_tensors="pt").to(device)  # Move inputs to device

    # Get image features
    with torch.no_grad():
        image_features = model.get_image_features(**inputs)
        # Normalize
        image_features = image_features / image_features.norm(dim=-1, keepdim=True)

    return image_features.cpu().numpy().flatten()  # Move back to CPU for numpy

from tqdm import tqdm

def store_images_in_chroma(catalog_items):
    """Store all images from catalog_items in ChromaDB"""
    
    collection = client.get_or_create_collection(
        name="catalog_images",
        metadata={"hnsw:space": "cosine"}
    )
    ids = []
    embeddings = []
    metadatas = []
    documents = []
    
    # for item in catalog_items:
    for item in tqdm(catalog_items, desc="Processing images"):
        image_path = item['image_path']
        
        if image_path and os.path.exists(image_path):
            embedding = get_image_embedding(image_path)
            
            ids.append(f"item_{item['item_number']}")
            embeddings.append(embedding.tolist())
            metadatas.append({
                'item_number': int(item['item_number']),
                'title': str(item['title']),
                'description': str(item['description']),
                'page': int(item['page']),
                'image_path': str(item['image_path'])
            })
            documents.append(f"Item {item['item_number']}: {item['title']}")
    
    collection.add(
        ids=ids,
        embeddings=embeddings,
        metadatas=metadatas,
        documents=documents
    )
    
    print(f"Stored {len(ids)} images in ChromaDB")
    return collection

def ingest_catalog_pdf(pdf_base64):
    catalog_items = extract_catalog_items(pdf_base64)
    # display_items(catalog_items[:3])
    collection = store_images_in_chroma(catalog_items)

    # Get all data from collection
    results = collection.get()

    # Print count
    print(f"Total items: {len(results['ids'])}")
